#!/usr/bin/env python3
"""
Test the trade management fixes
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from trade_management.trade_manager import TradeManager
from account_management.account_manager import AccountManager
from mt5_integration.mt5_client import MT5Client
from account_management.models import TradingAccount
from logging_system.logger import setup_logger

# Setup logging
logger = setup_logger()

async def test_trade_management_fixes():
    """Test the trade management fixes"""
    print("🔧 TESTING TRADE MANAGEMENT FIXES")
    print("=" * 60)
    
    try:
        # Initialize account manager
        account_manager = AccountManager()
        
        # Create a test account directly (bypass config issues)
        test_account = TradingAccount(
            account_id="demo1",
            account_number=********,
            server="RoboForex-ECN",
            username="********",
            password="Daadb123",
            strategy_type="mean_reversion",
            money_management_type="martingale",
            symbols=[
                {"symbol": "EURUSD", "timeframe": "M15"},
                {"symbol": "GBPUSD", "timeframe": "M15"},
                {"symbol": "USDJPY", "timeframe": "M15"}  # Add USDJPY to config
            ],
            timeframes=["M15"]
        )
        
        # Initialize trade manager
        trade_manager = TradeManager(account_manager)
        
        print("✅ Trade manager initialized")
        
        # Test MT5 connection and session management
        mt5_client = MT5Client()
        if not mt5_client.initialize():
            print("❌ Failed to initialize MT5")
            return False
        
        print("✅ MT5 initialized")
        
        # Test session management fix
        session_success = await trade_manager.session_manager.ensure_account_session(test_account, mt5_client)
        
        if session_success:
            print("✅ Session management working")
            print(f"   Current account in MT5Client: {mt5_client.current_account.account_id if mt5_client.current_account else 'None'}")
        else:
            print("❌ Session management failed")
            return False
        
        # Test getting positions and orders
        print("\n📊 Testing data retrieval...")
        
        positions = mt5_client.get_positions()
        orders = mt5_client.get_pending_orders()
        
        print(f"✅ Retrieved {len(positions)} positions")
        print(f"✅ Retrieved {len(orders)} pending orders")
        
        # Test the fixed trade management logic
        print("\n🔄 Testing trade management logic...")
        
        # Manually call the account trade management
        from ai_integration.qwen_client import QwenClient
        
        async with QwenClient() as qwen_client:
            await trade_manager._manage_account_trades(test_account, qwen_client)
        
        print("✅ Trade management completed without errors")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        logger.error(f"Test error: {e}", exc_info=True)
        return False
        
    finally:
        # Cleanup
        if 'mt5_client' in locals():
            mt5_client.shutdown()
        print("🔒 MT5 connection closed")

async def main():
    """Main test function"""
    success = await test_trade_management_fixes()
    
    if success:
        print("\n🎉 ALL FIXES WORKING CORRECTLY")
        print("✅ Session management fixed")
        print("✅ Pending order management fixed") 
        print("✅ Error handling improved")
        print("✅ USDJPY orders should now be managed")
    else:
        print("\n❌ SOME FIXES NEED MORE WORK")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
